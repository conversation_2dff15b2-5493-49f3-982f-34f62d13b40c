import { Link, RichText, Text } from '@sitecore-jss/sitecore-jss-nextjs';
import { useState } from 'react';
import { SelectedPlanCardContainerProps } from 'components/common/SelectedPlanCardContainer/SelectedPlanCardContainer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChevronDown as faChevronDownSolid,
  faChevronUp as faChevronUpSolid,
  faFileAlt,
} from '@fortawesome/pro-solid-svg-icons';
import { faXmark } from '@fortawesome/pro-light-svg-icons';
import Tooltip from 'components/Elements/Tooltip/Tooltip';
import QuestionCircle from 'assets/icons/QuestionCircle';
import DownloadIcon from 'assets/icons/DownloadIcon';
import Incentive from 'components/Elements/Incentive/Incentive';
import { useRouter } from 'next/router';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';

interface SelectedPlanCardProps {
  variant: 'primary' | 'secondary';
  planName: string;
  rate: number;
  term?: number;
  cancellationFee?: string;
  EFLUrl?: string;
  TOSUrl: string;
  YRCUrl?: string;
  incentiveId?: string;
  incentiveText?: string;
  incentiveDisclaimer?: string;
  fields: Partial<SelectedPlanCardContainerProps>;
}

const SelectedPlanCard = (props: SelectedPlanCardProps): JSX.Element => {
  // fix
  const [showDetails, setShowDetails] = useState(false);
  const isPrimary = props.variant === 'primary';
  const router = useRouter();
  const { web_experienceid } = router.query as QueryParamsMapType;

  const cancellationfee = props.fields.fields?.EarlyCancellationFeeText.value.replace(
    '${cancellationfee}',
    props.cancellationFee ? props.cancellationFee : '0'
  );

  //Web Experienceid in ADL
  if (window.adobeDataLayer && web_experienceid !== '' && web_experienceid !== undefined)
    window.adobeDataLayer.ExperienceID = web_experienceid;
  else if (window.adobeDataLayer)
    window.adobeDataLayer.ExperienceID = process.env.NEXT_PUBLIC_SITE_NAME as string;

  return (
    <div
      className={`w-full sm:w-fit sm:pt-2 p-0 shadow-none bg-white flex flex-col font-primaryRegular text-textQuattuordenary gap-3 rounded-xl sm:rounded-lg sm:ml-[-38px]  ${
        showDetails ? `z-9 relative sm:rounded-xl rounded-0` : 'sm:rounded-lg'
      }`}
    >
      <div
        className={`flex gap-3 ${
          isPrimary
            ? 'flex-col sm:flex-row items-start sm:items-center relative top-[11px]'
            : 'flex-row'
        }`}
      >
        <Text
          tag="p"
          className="text-[14px] leading-[16px] text-textQuattuordenary font-primaryRegular sm:mt-4 block"
        />
        <RichText
          tag="p"
          className="font-primaryBold text-[16px] leading-[20px] text-textQuattuordenary"
          field={{ value: props.planName }}
        />
        <div
          className={`border-l-2 border-cactus h-6 ${isPrimary ? 'hidden sm:block' : 'block'}`}
        />
        <div className="flex flex-row gap-3 items-center">
          <RichText
            tag="p"
            className="text-[14px] leading-[16px] text-textQuattuordenary"
            field={{
              value: `${props.fields.fields?.RateText.value} ${
                isPrimary ? `${(props.rate * 100).toFixed(1)}\u00A2` : `\u0024${props.rate}/mo`
              }`,
            }}
          />
          <div className={` ${isPrimary ? 'hidden sm:block' : 'block'}`} />
          <Text
            tag="p"
            className={`text-[14px] leading-[16px] font-primaryRegular ${isPrimary || 'hidden'}`}
            field={{
              value: `${props.fields.fields?.TermText.value} ${
                props.term ? `${props.term} Months` : 'Month to Month'
              }`,
            }}
          />
          <div className={` ${isPrimary ? 'hidden sm:block' : 'block'}`} />
          <div className="sm:text-textPrimary sm:hover:text-textSecondary hidden sm:flex">
            <p
              onClick={() => setShowDetails(!showDetails)}
              className="text-minus2 hidden sm:block text-textPrimary hover:text-textSecondary font-primaryBold text-center select-none cursor-pointer"
            >
              {showDetails
                ? `${props.fields.fields?.HideDetailsText.value}`
                : `${props.fields.fields?.SeeDetailsText.value}`}
              <FontAwesomeIcon
                className=" hidden  cursor-pointer"
                icon={showDetails ? faChevronUpSolid : faChevronDownSolid}
              />
            </p>
          </div>
          {showDetails && (
            <FontAwesomeIcon
              icon={faXmark}
              className="text-textPrimary hover:text-textSecondary cursor-pointer hidden sm:block text-[22px]"
              onClick={() => setShowDetails(false)}
            />
          )}
        </div>
      </div>
      <div
        className={`flex flex-col gap-3 sm:pl-[65px] sm:px-[20px] sm:py-[15px]  ${
          showDetails
            ? 'sm:flex z-999 bg-white     sm:ml-[-20px] sm:max-w-[1270px] absolute top-[50px] w-full left-[20.7%] rounded border border-[#297F9D] shadow-[0px_8px_32px_-8px_rgba(63,71,90,0.4)]'
            : 'sm:hidden'
        }`}
      >
        <div className="flex gap-2 selected-plan-tooltip">
          <Text
            tag="p"
            className={`font-primaryBold text-[16px] leading-[20px] text-textQuattuordenary`}
            field={{
              value: `${
                isPrimary
                  ? props.fields.fields?.PlanDetailsandInformationText.value
                  : props.fields.fields?.ProductDetailsandInformationText.value
              }`,
            }}
          />
          <Tooltip
            content={
              props.fields.fields?.EarlyCancellationFeeTooltip
                ? props.fields.fields?.EarlyCancellationFeeTooltip
                : { value: '' }
            }
            className={`${
              isPrimary ? 'selected-tooltip selected-tooltip-mobile' : 'addonplan-tooltip'
            }`}
            arrowclassName="selected-tooltip-icon"
          >
            <QuestionCircle />
          </Tooltip>
        </div>
        <div className="flex flex-col gap-4 custom-download">
          {props.incentiveText && (
            <Incentive
              className={`w-full sm:w-[304px] sm:h-10 inc_design`}
              content={props.incentiveText}
            />
          )}
          <div className={`flex items-center max-w-[200px] sm:max-w-full ${isPrimary || 'hidden'}`}>
            <Text
              tag="p"
              className={`text-[16px] leading-[20px] py-2 ${isPrimary || 'hidden'}`}
              field={{
                value: `${cancellationfee}`,
              }}
            />
            <div className={'flex-shrink-0 block w-auto text-[16px] leading-[20px]'}></div>
          </div>
          <Link
            field={{ value: { href: props.EFLUrl } }}
            className={`text-textPrimary hover:text-textSecondary font-primaryBold text-[16px] leading-[20px] flex flex-wrap w-full items-center ${
              isPrimary || 'hidden'
            }`}
            target="_blank"
          >
            {props.fields.fields?.ElectricityFactsLabelText.value}
            <span className="relative pl-3 ">
              <span className="hidden">
                <DownloadIcon />
              </span>
              <span className="block text-textPrimary text-minus2">
                <FontAwesomeIcon icon={faFileAlt} />
              </span>
            </span>
          </Link>
          <Link
            field={{ value: { href: props.TOSUrl } }}
            className="flex text-textPrimary hover:text-textSecondary font-primaryBold text-[16px]  leading-[20px]  w-full items-center"
            target="_blank"
          >
            {isPrimary ? props.fields.fields?.TermsOfServiceText.value : 'Terms and Conditions'}
            <span className={`relative pl-3 ${isPrimary || 'hidden'}`}>
              <span className="hidden">
                <DownloadIcon />
              </span>
              <span className="block text-textPrimary text-minus2">
                <FontAwesomeIcon icon={faFileAlt} />
              </span>
            </span>
          </Link>
          <Link
            field={{ value: { href: props.YRCUrl } }}
            className={`flex text-textPrimary hover:text-textSecondary font-primaryBold text-[16px] leading-[20px] items-center ${
              isPrimary || 'hidden'
            }`}
            target="_blank"
          >
            {props.fields.fields?.YourRightsAsACustomerText.value}
            <span className="relative pl-3">
              <span className="hidden">
                <DownloadIcon />
              </span>
              <span className="block text-textPrimary text-minus2">
                <FontAwesomeIcon icon={faFileAlt} />
              </span>
            </span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SelectedPlanCard;
